# Admin Server Deployment Checklist

## Pre-Deployment Steps
- [ ] Admin database created and configured
- [ ] Environment variables updated in .env.production
- [ ] NEXTAUTH_URL updated to admin domain
- [ ] DATABASE_URL points to admin database
- [ ] Inter-server communication secrets configured

## Repository Setup
- [ ] Repository pushed to GitHub as inno-crm-admin
- [ ] All admin configuration files in place
- [ ] package.json updated with admin settings
- [ ] vercel.json configured for admin server

## Vercel Deployment
- [ ] Project imported to Vercel as inno-crm-admin
- [ ] Environment variables set in Vercel dashboard
- [ ] Build and deployment successful
- [ ] Health check endpoint responding

## Testing
- [ ] Admin login working
- [ ] Cashier login working
- [ ] All admin features accessible
- [ ] Analytics dashboard working
- [ ] Payment management working
- [ ] Financial reports working
- [ ] Inter-server communication working

## Security
- [ ] IP whitelist configured (if needed)
- [ ] CORS settings verified
- [ ] Authentication working properly
- [ ] Role-based access control verified

## Monitoring
- [ ] Health checks setup
- [ ] Error monitoring configured
- [ ] Performance monitoring active
- [ ] Database connection monitoring

## Documentation
- [ ] Admin server documentation updated
- [ ] Deployment guide reviewed
- [ ] Team notified of admin server URL
- [ ] Access credentials distributed securely
