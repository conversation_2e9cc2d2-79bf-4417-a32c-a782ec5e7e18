// Admin Server Middleware Configuration
// This file should be used to update middleware.ts for admin server

export const ADMIN_SERVER_CONFIG = {
  allowedRoles: ['ADMIN', 'CASHIER', 'MANAGER', 'TEACHER', 'RECEPTION', '<PERSON><PERSON><PERSON><PERSON>_MANAGER', 'STUDENT'],
  adminOnlyRoutes: [
    '/dashboard/admin',
    '/dashboard/analytics',
    '/dashboard/payments',
    '/api/analytics',
    '/api/reports',
    '/api/payments',
    '/api/kpis',
    '/api/activity-logs'
  ],
  serverType: 'admin'
};

// Update your middleware.ts to use this configuration
// Example:
// const config = process.env.SERVER_TYPE === 'admin' ? ADMIN_SERVER_CONFIG : STAFF_SERVER_CONFIG;
